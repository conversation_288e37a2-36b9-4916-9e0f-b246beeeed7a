<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件大小估算准确性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .scenario {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .comparison div {
            padding: 8px;
            border-radius: 3px;
            text-align: center;
        }
        .old-method {
            background: #f8d7da;
        }
        .new-method {
            background: #d4edda;
        }
        .actual {
            background: #cce5ff;
        }
    </style>
</head>
<body>
    <h1>文件大小估算准确性测试</h1>
    <p>测试修复后的文件大小估算是否与实际下载大小一致。</p>

    <div class="test-case">
        <h3>问题重现：您的场景</h3>
        <div class="scenario">
            场景：显示 16.5 KB，实际下载 91 KB，差异巨大
        </div>
        <button onclick="testYourScenario()">测试您的场景</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-case">
        <h3>估算方法对比测试</h3>
        <div class="scenario">
            对比旧方法和新方法的估算准确性
        </div>
        <button onclick="testEstimationMethods()">测试估算方法</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-case">
        <h3>实际文件大小测试</h3>
        <div class="scenario">
            创建实际的图片文件并测试大小计算
        </div>
        <button onclick="testActualFileSize()">测试实际文件大小</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        // 旧的估算方法（不准确）
        function oldEstimateFileSize(dataUrl) {
            const base64Data = dataUrl.split(',')[1];
            if (!base64Data) return 0;
            return Math.round((base64Data.length * 3) / 4);
        }

        // 新的估算方法（更准确）
        function newEstimateFileSize(dataUrl) {
            const base64Data = dataUrl.split(',')[1];
            if (!base64Data) return 0;
            const padding = (base64Data.match(/=/g) || []).length;
            return Math.floor((base64Data.length * 3) / 4) - padding;
        }

        // 获取实际文件大小（通过 Blob）
        async function getActualFileSize(dataUrl) {
            try {
                const response = await fetch(dataUrl);
                const blob = await response.blob();
                return blob.size;
            } catch (error) {
                console.error('获取实际文件大小失败:', error);
                return 0;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function testYourScenario() {
            const resultDiv = document.getElementById('result1');
            
            // 模拟您的场景数据
            const scenarios = [
                {
                    name: '您的场景',
                    displayedSize: '16.5 KB',
                    actualDownloadSize: '91 KB',
                    estimatedBytes: 16896, // 16.5 * 1024
                    actualBytes: 93184 // 91 * 1024
                }
            ];

            let output = '🔍 问题分析：\n\n';
            
            scenarios.forEach(scenario => {
                const ratio = scenario.actualBytes / scenario.estimatedBytes;
                const difference = scenario.actualBytes - scenario.estimatedBytes;
                
                output += `场景: ${scenario.name}\n`;
                output += `显示大小: ${scenario.displayedSize} (${scenario.estimatedBytes} 字节)\n`;
                output += `实际大小: ${scenario.actualDownloadSize} (${scenario.actualBytes} 字节)\n`;
                output += `差异: ${formatFileSize(difference)} (${(ratio * 100 - 100).toFixed(1)}% 更大)\n`;
                output += `比例: 实际大小是显示大小的 ${ratio.toFixed(2)} 倍\n\n`;
            });

            output += '🔧 问题原因：\n';
            output += '1. 估算方法不考虑 Base64 填充字符\n';
            output += '2. 不同压缩算法的实际效果差异\n';
            output += '3. 浏览器下载时的重新编码\n\n';

            output += '✅ 修复方案：\n';
            output += '1. 使用更准确的文件大小计算方法\n';
            output += '2. 考虑 Base64 填充字符的影响\n';
            output += '3. 统一估算和实际计算的方法';

            resultDiv.className = 'result warning';
            resultDiv.textContent = output;
        }

        async function testEstimationMethods() {
            const resultDiv = document.getElementById('result2');
            resultDiv.textContent = '正在测试估算方法...';

            // 创建一个测试图片
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // 绘制一些内容
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(0, 0, 200, 150);
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(200, 150, 200, 150);
            ctx.fillStyle = '#45b7d1';
            ctx.fillRect(0, 150, 200, 150);
            ctx.fillStyle = '#f9ca24';
            ctx.fillRect(200, 0, 200, 150);

            // 测试不同格式
            const formats = [
                { format: 'image/png', quality: undefined, name: 'PNG' },
                { format: 'image/jpeg', quality: 0.9, name: 'JPEG (90%)' },
                { format: 'image/jpeg', quality: 0.7, name: 'JPEG (70%)' },
                { format: 'image/jpeg', quality: 0.5, name: 'JPEG (50%)' }
            ];

            let output = '📊 估算方法对比测试结果：\n\n';

            for (const fmt of formats) {
                const dataUrl = canvas.toDataURL(fmt.format, fmt.quality);
                
                const oldEstimate = oldEstimateFileSize(dataUrl);
                const newEstimate = newEstimateFileSize(dataUrl);
                const actualSize = await getActualFileSize(dataUrl);

                const oldError = Math.abs(actualSize - oldEstimate) / actualSize * 100;
                const newError = Math.abs(actualSize - newEstimate) / actualSize * 100;

                output += `${fmt.name}:\n`;
                output += `  旧方法: ${formatFileSize(oldEstimate)} (误差: ${oldError.toFixed(1)}%)\n`;
                output += `  新方法: ${formatFileSize(newEstimate)} (误差: ${newError.toFixed(1)}%)\n`;
                output += `  实际值: ${formatFileSize(actualSize)}\n`;
                output += `  改进: ${newError < oldError ? '✅ 更准确' : '❌ 需要进一步优化'}\n\n`;
            }

            resultDiv.className = 'result success';
            resultDiv.textContent = output;
        }

        async function testActualFileSize() {
            const resultDiv = document.getElementById('result3');
            resultDiv.textContent = '正在创建和测试实际文件...';

            // 创建一个更复杂的测试图片
            const canvas = document.createElement('canvas');
            canvas.width = 800;
            canvas.height = 600;
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 600);
            gradient.addColorStop(0, '#ff9a9e');
            gradient.addColorStop(0.5, '#fecfef');
            gradient.addColorStop(1, '#fecfef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 600);

            // 添加一些图形
            for (let i = 0; i < 20; i++) {
                ctx.fillStyle = `hsl(${i * 18}, 70%, 60%)`;
                ctx.fillRect(i * 40, i * 30, 50, 50);
            }

            // 测试压缩后的大小
            const highQuality = canvas.toDataURL('image/jpeg', 0.9);
            const mediumQuality = canvas.toDataURL('image/jpeg', 0.7);
            const lowQuality = canvas.toDataURL('image/jpeg', 0.5);

            const results = [];
            const testCases = [
                { name: '高质量 (90%)', dataUrl: highQuality },
                { name: '中等质量 (70%)', dataUrl: mediumQuality },
                { name: '低质量 (50%)', dataUrl: lowQuality }
            ];

            for (const testCase of testCases) {
                const oldEst = oldEstimateFileSize(testCase.dataUrl);
                const newEst = newEstimateFileSize(testCase.dataUrl);
                const actual = await getActualFileSize(testCase.dataUrl);

                results.push({
                    name: testCase.name,
                    oldEstimate: oldEst,
                    newEstimate: newEst,
                    actual: actual,
                    oldError: Math.abs(actual - oldEst) / actual * 100,
                    newError: Math.abs(actual - newEst) / actual * 100
                });
            }

            let output = '🧪 实际文件大小测试结果：\n\n';
            
            results.forEach(result => {
                output += `${result.name}:\n`;
                output += `  旧估算: ${formatFileSize(result.oldEstimate)} (误差: ${result.oldError.toFixed(1)}%)\n`;
                output += `  新估算: ${formatFileSize(result.newEstimate)} (误差: ${result.newError.toFixed(1)}%)\n`;
                output += `  实际值: ${formatFileSize(result.actual)}\n`;
                output += `  改进度: ${(result.oldError - result.newError).toFixed(1)}% 更准确\n\n`;
            });

            const avgOldError = results.reduce((sum, r) => sum + r.oldError, 0) / results.length;
            const avgNewError = results.reduce((sum, r) => sum + r.newError, 0) / results.length;

            output += `📈 总体改进：\n`;
            output += `平均误差从 ${avgOldError.toFixed(1)}% 降低到 ${avgNewError.toFixed(1)}%\n`;
            output += `准确性提升: ${(avgOldError - avgNewError).toFixed(1)}%`;

            resultDiv.className = avgNewError < avgOldError ? 'result success' : 'result warning';
            resultDiv.textContent = output;
        }

        // 页面加载时显示说明
        console.log('文件大小估算准确性测试页面已加载');
    </script>
</body>
</html>
