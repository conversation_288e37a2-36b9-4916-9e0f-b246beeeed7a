/**
 * 检测是否为移动设备
 * 通过 User Agent 来判断设备类型
 */
export function isMobileDevice(userAgent: string): boolean {
  const mobileRegex =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|Tablet|Pad|Kindle|Silk.*Accelerated|(Windows.*Touch)/i;
  // 特别检测iPad（包括新版Safari的User-Agent）
  const isSafariPad =
    /iPad|Macintosh.*AppleWebKit.*Safari/i.test(userAgent) &&
    !/Macintosh.*Chrome|Macintosh.*Firefox/i.test(userAgent);
  return mobileRegex.test(userAgent) || isSafariPad;
}

/**
 * 检测是否为触摸设备
 * 在客户端环境下检测触摸支持
 */
export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * 获取设备类型
 * 结合多种检测方式来判断设备类型
 */
export function getDeviceType(userAgent?: string): 'mobile' | 'desktop' {
  // 服务端环境，使用 User Agent 检测
  if (userAgent) {
    return isMobileDevice(userAgent) ? 'mobile' : 'desktop';
  }

  // 客户端环境，结合屏幕尺寸和触摸支持检测
  if (typeof window !== 'undefined') {
    const isMobileByScreen = window.innerWidth <= 768;
    const isMobileByTouch = isTouchDevice();
    const isMobileByUserAgent = isMobileDevice(navigator.userAgent);

    // 如果满足任意两个条件，则认为是移动设备
    const mobileIndicators = [
      isMobileByScreen,
      isMobileByTouch,
      isMobileByUserAgent,
    ];
    const mobileCount = mobileIndicators.filter(Boolean).length;

    return mobileCount >= 2 ? 'mobile' : 'desktop';
  }

  // 默认返回桌面端
  return 'desktop';
}
