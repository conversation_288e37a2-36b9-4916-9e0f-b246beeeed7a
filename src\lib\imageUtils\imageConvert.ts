/**
 * 图片格式转换工具函数
 */

// 支持的图片格式
export type SupportedFormat =
  | 'png'
  | 'jpg'
  | 'jpeg'
  | 'webp'
  | 'bmp'
  | 'xbm'
  | 'xpm';

// 图片格式转换质量设置
export interface ConvertOptions {
  quality?: number; // 0.0 - 1.0，仅对 JPG/JPEG 格式有效
  keepTransparency?: boolean; // 是否保持透明度（PNG、WEBP支持）
}

/**
 * 从文件名或MIME类型中提取图片格式
 */
export function getImageFormatFromFile(file: File): string {
  // 首先尝试从 MIME 类型获取
  if (file.type) {
    const mimeToFormat: Record<string, string> = {
      'image/png': 'png',
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/webp': 'webp',
      'image/bmp': 'bmp',
      'image/x-xbitmap': 'xbm',
      'image/x-xpixmap': 'xpm',
    };

    const format = mimeToFormat[file.type.toLowerCase()];
    if (format) {
      return format;
    }
  }

  // 从文件名扩展名获取
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (extension) {
    const extensionToFormat: Record<string, string> = {
      png: 'png',
      jpg: 'jpg',
      jpeg: 'jpg',
      webp: 'webp',
      bmp: 'bmp',
      xbm: 'xbm',
      xpm: 'xpm',
    };

    return extensionToFormat[extension] || 'png';
  }

  // 默认返回 PNG
  return 'png';
}

/**
 * 从URL或数据URL中提取图片格式
 */
export function getImageFormatFromUrl(url: string): string {
  if (url.startsWith('data:')) {
    // 数据URL格式：data:image/png;base64,xxx
    const match = url.match(/data:image\/([^;]+)/);
    if (match && match[1]) {
      const mimeFormat = match[1].toLowerCase();
      return mimeFormat === 'jpeg' ? 'jpg' : mimeFormat;
    }
  } else {
    // 普通URL，从扩展名判断
    const extension = url.split('.').pop()?.split('?')[0]?.toLowerCase();
    if (extension) {
      const extensionToFormat: Record<string, string> = {
        png: 'png',
        jpg: 'jpg',
        jpeg: 'jpg',
        webp: 'webp',
        bmp: 'bmp',
        xbm: 'xbm',
        xpm: 'xpm',
      };

      return extensionToFormat[extension] || 'png';
    }
  }

  return 'png';
}

/**
 * 获取格式对应的MIME类型
 */
export function getFormatMimeType(format: string): string {
  const formatToMime: Record<string, string> = {
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    webp: 'image/webp',
    bmp: 'image/bmp',
    xbm: 'image/x-xbitmap',
    xpm: 'image/x-xpixmap',
  };

  return formatToMime[format.toLowerCase()] || 'image/png';
}

/**
 * 检查格式是否支持透明度
 */
export function formatSupportsTransparency(format: string): boolean {
  const transparentFormats = ['png', 'webp', 'xbm', 'xpm'];
  return transparentFormats.includes(format.toLowerCase());
}

/**
 * 计算文件大小（估算）
 */
function estimateFileSize(dataUrl: string): number {
  // 移除data URL前缀
  const base64Data = dataUrl.split(',')[1];
  if (!base64Data) return 0;

  // Base64编码后的大小约为原始大小的4/3
  return Math.round((base64Data.length * 3) / 4);
}

/**
 * 转换图片格式
 * @param imageUrl 源图片URL或数据URL
 * @param targetFormat 目标格式
 * @param options 转换选项
 * @returns Promise<{dataUrl: string, size: number}> 转换后的数据URL和文件大小
 */
export async function convertImageFormat(
  imageUrl: string,
  targetFormat: SupportedFormat,
  options: ConvertOptions = {}
): Promise<{ dataUrl: string; size: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取Canvas上下文'));
          return;
        }

        // 设置画布尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 设置背景色（针对不支持透明度的格式）
        if (!formatSupportsTransparency(targetFormat)) {
          ctx.fillStyle = '#FFFFFF'; // 白色背景
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制图片
        ctx.drawImage(img, 0, 0);

        // 获取MIME类型
        const mimeType = getFormatMimeType(targetFormat);

        // 设置质量（仅对JPEG格式有效）
        const quality = options.quality ?? 0.9;

        // 转换为目标格式
        const dataUrl = canvas.toDataURL(mimeType, quality);

        // 计算文件大小
        const size = estimateFileSize(dataUrl);

        resolve({ dataUrl, size });
      } catch (error) {
        reject(
          new Error(
            `格式转换失败: ${error instanceof Error ? error.message : '未知错误'}`
          )
        );
      }
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    // 设置跨域属性（如果需要）
    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  });
}

/**
 * 批量转换图片格式
 * @param imageUrls 图片URL数组
 * @param targetFormat 目标格式
 * @param options 转换选项
 * @param onProgress 进度回调
 * @returns Promise<{dataUrl: string, size: number}[]> 转换后的数据URL和大小数组
 */
export async function batchConvertImageFormat(
  imageUrls: string[],
  targetFormat: SupportedFormat,
  options: ConvertOptions = {},
  onProgress?: (current: number, total: number, currentUrl?: string) => void
): Promise<{ dataUrl: string; size: number }[]> {
  const results: { dataUrl: string; size: number }[] = [];
  const total = imageUrls.length;

  for (let i = 0; i < imageUrls.length; i++) {
    const url = imageUrls[i];

    try {
      const convertResult = await convertImageFormat(
        url,
        targetFormat,
        options
      );
      results.push(convertResult);

      if (onProgress) {
        onProgress(i + 1, total, url);
      }
    } catch (error) {
      console.error(`图片格式转换失败: ${url}`, error);
      // 转换失败时推入空结果
      results.push({ dataUrl: '', size: 0 });

      if (onProgress) {
        onProgress(i + 1, total, url);
      }
    }

    // 添加小延迟避免浏览器阻塞
    if (i < imageUrls.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  return results;
}

/**
 * 根据文件扩展名更新文件名
 */
export function updateFileNameExtension(
  fileName: string,
  newFormat: string
): string {
  const baseName = fileName.replace(/\.[^/.]+$/, ''); // 移除现有扩展名
  return `${baseName}.${newFormat.toLowerCase()}`;
}
