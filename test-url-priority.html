<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL优先级选择测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .scenario {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>URL优先级选择测试</h1>
    <p>测试修复后的URL选择逻辑，确保在复杂操作组合下能正确选择最佳输入URL。</p>

    <div class="test-case">
        <h3>测试案例 1: 完整操作链 - 只需要背景处理</h3>
        <div class="scenario">
            场景：图片已完成 去背→尺寸调整→格式转换→压缩，现在只需要添加背景
        </div>
        <button onclick="testCase1()">运行测试</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 2: 部分操作链 - 需要格式转换和压缩</h3>
        <div class="scenario">
            场景：图片已完成 去背→尺寸调整，现在需要格式转换和压缩
        </div>
        <button onclick="testCase2()">运行测试</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 3: 混合需求 - 需要尺寸调整但不需要压缩</h3>
        <div class="scenario">
            场景：图片已完成 去背→格式转换→压缩，现在需要尺寸调整
        </div>
        <button onclick="testCase3()">运行测试</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 4: 您提到的场景 - 尺寸调整丢失</h3>
        <div class="scenario">
            场景：图片同时有压缩、尺寸调整、格式转换、背景替换，需要背景处理
        </div>
        <button onclick="testCase4()">运行测试</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        // 模拟URL选择逻辑
        function selectBestInputUrl(image) {
            // 检查需要的处理
            const needsBackgroundProcessing =
                (image.backgroundColor && image.backgroundColor !== 'transparent') ||
                image.backgroundImageUrl;
            
            const needsResizing =
                image.targetWidth && image.targetHeight && !image.resizedUrl;
            
            const needsFormatConversion = image.convertedFormat && !image.convertedUrl;
            
            const needsCompression =
                (image.compressionLevel || (image.customCompressionSize && image.customCompressionUnit)) &&
                !image.compressedUrl;

            // 智能选择输入URL
            let inputUrl = image.previewUrl;
            let bestUrl = { 
                url: inputUrl, 
                score: 0, 
                name: 'previewUrl',
                operations: []
            };

            // 构建URL候选列表
            const urlCandidates = [
                {
                    url: image.processedUrl,
                    score: image.processedUrl ? 1 : 0,
                    name: 'processedUrl',
                    operations: ['background_removal']
                },
                {
                    url: image.resizedUrl,
                    score: image.resizedUrl ? (needsResizing ? 0 : 2) : 0,
                    name: 'resizedUrl',
                    operations: ['background_removal', 'resize']
                },
                {
                    url: image.convertedUrl,
                    score: image.convertedUrl ? (needsFormatConversion ? 0 : 3) : 0,
                    name: 'convertedUrl',
                    operations: ['background_removal', 'resize', 'format_conversion']
                },
                {
                    url: image.compressedUrl,
                    score: image.compressedUrl ? (needsCompression ? 0 : 4) : 0,
                    name: 'compressedUrl',
                    operations: ['background_removal', 'resize', 'format_conversion', 'compression']
                }
            ];

            // 选择得分最高的URL
            for (const candidate of urlCandidates) {
                if (candidate.url && candidate.score > bestUrl.score) {
                    bestUrl = {
                        url: candidate.url,
                        score: candidate.score,
                        name: candidate.name,
                        operations: candidate.operations
                    };
                }
            }

            return {
                selectedUrl: bestUrl,
                needsProcessing: {
                    needsBackgroundProcessing,
                    needsResizing,
                    needsFormatConversion,
                    needsCompression
                }
            };
        }

        function testCase1() {
            const image = {
                name: '完整操作链.png',
                previewUrl: 'blob://original',
                processedUrl: 'blob://processed',
                resizedUrl: 'blob://resized',
                convertedUrl: 'blob://converted',
                compressedUrl: 'blob://compressed',
                backgroundColor: '#ffffff', // 需要背景处理
                targetWidth: 400,
                targetHeight: 300,
                convertedFormat: 'jpg',
                compressionLevel: 'medium'
            };

            const result = selectBestInputUrl(image);
            const resultDiv = document.getElementById('result1');
            
            // 应该选择compressedUrl，因为它包含最多已完成的操作
            if (result.selectedUrl.name === 'compressedUrl' && result.selectedUrl.score === 4) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ 测试通过<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    包含操作: ${result.selectedUrl.operations.join(', ')}<br>
                    需要处理: 背景=${result.needsProcessing.needsBackgroundProcessing}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ 测试失败<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    期望: compressedUrl (得分: 4)
                `;
            }
        }

        function testCase2() {
            const image = {
                name: '部分操作链.png',
                previewUrl: 'blob://original',
                processedUrl: 'blob://processed',
                resizedUrl: 'blob://resized',
                // 没有convertedUrl和compressedUrl
                targetWidth: 400,
                targetHeight: 300,
                convertedFormat: 'jpg', // 需要格式转换
                compressionLevel: 'medium' // 需要压缩
            };

            const result = selectBestInputUrl(image);
            const resultDiv = document.getElementById('result2');
            
            // 应该选择resizedUrl，因为它是最高级别的已完成操作
            if (result.selectedUrl.name === 'resizedUrl' && result.selectedUrl.score === 2) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ 测试通过<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    包含操作: ${result.selectedUrl.operations.join(', ')}<br>
                    需要处理: 格式转换=${result.needsProcessing.needsFormatConversion}, 压缩=${result.needsProcessing.needsCompression}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ 测试失败<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    期望: resizedUrl (得分: 2)
                `;
            }
        }

        function testCase3() {
            const image = {
                name: '混合需求.png',
                previewUrl: 'blob://original',
                processedUrl: 'blob://processed',
                // 没有resizedUrl，但有后续操作
                convertedUrl: 'blob://converted',
                compressedUrl: 'blob://compressed',
                targetWidth: 400, // 需要尺寸调整
                targetHeight: 300,
                convertedFormat: 'jpg',
                compressionLevel: 'medium'
            };

            const result = selectBestInputUrl(image);
            const resultDiv = document.getElementById('result3');
            
            // 应该选择processedUrl，因为需要尺寸调整，不能使用包含尺寸调整的URL
            if (result.selectedUrl.name === 'processedUrl' && result.selectedUrl.score === 1) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ 测试通过<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    包含操作: ${result.selectedUrl.operations.join(', ')}<br>
                    需要处理: 尺寸调整=${result.needsProcessing.needsResizing}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ 测试失败<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    期望: processedUrl (得分: 1)<br>
                    原因: 需要尺寸调整，不能使用包含尺寸调整的URL
                `;
            }
        }

        function testCase4() {
            const image = {
                name: '您的场景.png',
                previewUrl: 'blob://original',
                processedUrl: 'blob://processed',
                resizedUrl: 'blob://resized',
                convertedUrl: 'blob://converted',
                compressedUrl: 'blob://compressed',
                backgroundColor: '#ffffff', // 需要背景处理
                targetWidth: 400,
                targetHeight: 300,
                convertedFormat: 'jpg',
                compressionLevel: 'medium'
            };

            const result = selectBestInputUrl(image);
            const resultDiv = document.getElementById('result4');
            
            // 应该选择compressedUrl，保留所有已完成的操作（包括尺寸调整）
            if (result.selectedUrl.name === 'compressedUrl' && 
                result.selectedUrl.operations.includes('resize')) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    ✅ 测试通过 - 尺寸调整不会丢失！<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    包含操作: ${result.selectedUrl.operations.join(', ')}<br>
                    ✅ 尺寸调整已保留在选择的URL中<br>
                    需要处理: 背景=${result.needsProcessing.needsBackgroundProcessing}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ 测试失败<br>
                    选择的URL: ${result.selectedUrl.name} (得分: ${result.selectedUrl.score})<br>
                    包含操作: ${result.selectedUrl.operations.join(', ')}<br>
                    ❌ 尺寸调整可能丢失
                `;
            }
        }

        // 页面加载时显示说明
        console.log('URL优先级测试页面已加载');
    </script>
</body>
</html>
